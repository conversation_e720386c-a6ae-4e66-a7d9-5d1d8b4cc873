/// 바라 부스 매니저 - 인앱 구독 서비스
///
/// iOS App Store와 Google Play Store의 인앱 구독을 관리하는 서비스입니다.
/// - 플랫폼별 인앱 구독 상품 관리
/// - 구독 상태 확인 및 복원
/// - Firebase와 구독 상태 동기화
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 1월
library;

import 'dart:async';
import 'dart:io';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';
import '../models/subscription_plan.dart';
import 'subscription_service.dart';
import '../providers/subscription_provider.dart';

/// 인앱 구독 서비스
class InAppPurchaseService {
  static const String _tag = 'InAppPurchaseService';

  // 인앱 구독 상품 ID들 (구글/애플 공통)
  static const String _plusPlanProductId = 'barabooth_plus_monthly';

  // Firebase 인스턴스
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // InAppPurchase 인스턴스
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // 구독 상태 스트림
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  // Riverpod Ref for provider invalidation
  WidgetRef? _ref;

  /// 싱글톤 인스턴스 (지연 초기화로 Firebase.initializeApp() 이후 생성되도록)
  static InAppPurchaseService? _instance;
  factory InAppPurchaseService() {
    return _instance ??= InAppPurchaseService._internal();
  }
  InAppPurchaseService._internal();

  /// Riverpod Ref 설정 (Provider 갱신을 위해)
  void setRef(WidgetRef ref) {
    _ref = ref;
  }

  // iOS   최근 결제 시도 감지용 플래그
  DateTime? _recentPurchaseAttemptAt;
  bool _restoreTriggeredAfterAttempt = false;

  // 🔐 구매 시작 시점의 사용자 ID 저장 (iOS 인증 상태 문제 해결)
  String? _purchaseStartUserId;

  /// 현재 로그인된 사용자 ID 가져오기
  String? get _currentUserId => _auth.currentUser?.uid;

  bool _isInitialized = false;

  /// 서비스 초기화
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggerUtils.logInfo('인앱 구독 서비스 이미 초기화됨 - 재초기화 스킵', tag: _tag);
      return;
    }
    try {
      LoggerUtils.logInfo('인앱 구독 서비스 초기화 시작', tag: _tag);

      // 인앱 구매 사용 가능 여부 확인
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        LoggerUtils.logError('인앱 구매를 사용할 수 없습니다', tag: _tag);
        return;
      }

      // 플랫폼별 설정
      if (Platform.isAndroid) {
        // Android 특정 설정 (enablePendingPurchases는 더 이상 필요하지 않음)
        LoggerUtils.logInfo('🤖 Android 플랫폼 감지됨', tag: _tag);
      } else if (Platform.isIOS) {
        // iOS 특정 설정
        print('🍎 [iOS] 플랫폼 감지됨');

        // iOS에서 앱 시작 시 한 번 복원 호출하여 미처리된 구매 확인
        try {
          print('🍎 [iOS] 앱 시작 시 구매 복원 확인');
          await _inAppPurchase.restorePurchases();
          print('🍎 [iOS] 구매 복원 확인 완료');
        } catch (e) {
          print('🍎 [iOS] 구매 복원 확인 실패: $e');
        }
      }

      // 구매 상태 변경 리스너 설정 (중복 연결 방지)
      LoggerUtils.logInfo('🔄 구매 스트림 리스너 설정 중...', tag: _tag);
      _subscription ??= _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdated,
        onDone: () => LoggerUtils.logInfo('📡 구매 스트림 종료', tag: _tag),
        onError: (error) => LoggerUtils.logError('📡 구매 스트림 오류', tag: _tag, error: error),
      );
      LoggerUtils.logInfo('✅ 구매 스트림 리스너 설정 완료', tag: _tag);


      _isInitialized = true;
      LoggerUtils.logInfo('인앱 구독 서비스 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('인앱 구독 서비스 초기화 실패', tag: _tag, error: e);
    }
  }

  /// 서비스 종료 (싱글톤: 화면에서 임의로 호출하지 말 것)
  void dispose() {
    try {
      _subscription?.cancel();
      _subscription = null;
      _isInitialized = false;
      LoggerUtils.logInfo('인앱 구독 서비스 dispose 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('인앱 구독 서비스 dispose 중 오류', tag: _tag, error: e);
    }
  }

  /// iOS: 최근 결제 시도 직후 앱 복귀 시 한 번만 복원 호출해 이벤트 유실 대비
  Future<void> onAppResumed() async {
    if (!Platform.isIOS) return;

    try {
      print('🍎 [iOS] 앱 복귀 감지 - 구매 상태 확인 시작');

      final attemptAt = _recentPurchaseAttemptAt;
      print('🍎 [iOS] 최근 구매 시도 시간: $attemptAt');
      print('🍎 [iOS] 복원 트리거 여부: $_restoreTriggeredAfterAttempt');

      if (attemptAt == null || _restoreTriggeredAfterAttempt) {
        print('🍎 [iOS] 복원 조건 불충족 - 건너뜀');
        return;
      }

      final diff = DateTime.now().difference(attemptAt);
      print('🍎 [iOS] 구매 시도 후 경과 시간: ${diff.inMinutes}분');

      if (diff.inMinutes <= 5) { // 5분으로 확장
        print('🍎 [iOS] 최근 결제 시도 감지 - restorePurchases 트리거');
        await _inAppPurchase.restorePurchases();
        _restoreTriggeredAfterAttempt = true;
        print('🍎 [iOS] 구매 복원 완료');

        // 복원 후 잠시 대기하여 이벤트 처리 시간 확보
        await Future.delayed(const Duration(seconds: 2));
        print('🍎 [iOS] 구매 복원 후 대기 완료');
      } else {
        print('🍎 [iOS] 구매 시도가 너무 오래됨 - 복원 건너뜀');
      }
    } catch (e) {
      print('🍎 [iOS] onAppResumed 처리 실패: $e');
    }
  }


  /// 사용 가능한 구독 상품 조회
  Future<List<ProductDetails>> getAvailableProducts() async {
    try {
      LoggerUtils.logInfo('사용 가능한 구독 상품 조회 시작', tag: _tag);

      const Set<String> productIds = {_plusPlanProductId};
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        LoggerUtils.logError('상품 조회 실패', tag: _tag, error: response.error);
        return [];
      }

      LoggerUtils.logInfo('조회된 상품 수: ${response.productDetails.length}', tag: _tag);
      return response.productDetails;
    } catch (e) {
      LoggerUtils.logError('상품 조회 중 오류 발생', tag: _tag, error: e);
      return [];
    }
  }

  /// 플러스 플랜 구독 시작
  Future<bool> subscribeToPlusPlan() async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('로그인이 필요합니다', tag: _tag);
        return false;
      }

      // 🔐 구매 시작 시점의 사용자 ID 저장 (iOS 인증 상태 문제 해결)
      _purchaseStartUserId = _currentUserId;
      LoggerUtils.logInfo('플러스 플랜 구독 시작 - 사용자 ID 저장: $_purchaseStartUserId', tag: _tag);

      // 🔍 기존 활성 구독 확인 (iOS 자동 복원 방지)
      final activeSubscriptions = await getActiveSubscriptions();
      final existingPlusSubscription = activeSubscriptions.where(
        (subscription) => subscription.productID == _plusPlanProductId &&
                         subscription.status == PurchaseStatus.purchased
      ).firstOrNull;

      if (existingPlusSubscription != null) {
        LoggerUtils.logInfo('🔄 기존 활성 구독 발견 - 복원 처리 시작', tag: _tag);
        await _handleRestoredPurchase(existingPlusSubscription);
        return true;
      }

      // 상품 정보 조회
      final products = await getAvailableProducts();
      final plusProduct = products.where((product) => product.id == _plusPlanProductId).firstOrNull;

      if (plusProduct == null) {
        LoggerUtils.logError('플러스 플랜 상품을 찾을 수 없습니다', tag: _tag);
        return false;
      }

      // 구매 요청 (구독의 경우 buyNonConsumable 사용)
      final String? userForPurchase = _purchaseStartUserId ?? _currentUserId;
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: plusProduct,
        applicationUserName: userForPurchase, // iOS StoreKit 2 최적화: 사용자 식별자 연동
      );
      final bool success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      // iOS: 최근 결제 시도 플래그 기록 → 앱 복귀 시 복원 1회 트리거 근거로 사용
      if (success && Platform.isIOS) {
        _recentPurchaseAttemptAt = DateTime.now();
        _restoreTriggeredAfterAttempt = false;
        print('🍎 [iOS] 구매 시도 플래그 기록: ${_recentPurchaseAttemptAt}');
      }

      LoggerUtils.logInfo('구매 요청 결과: $success', tag: _tag);
      return success;
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 구독 복원
  Future<void> restorePurchases() async {
    try {
      LoggerUtils.logInfo('구독 복원 시작', tag: _tag);
      await _inAppPurchase.restorePurchases();
      LoggerUtils.logInfo('구독 복원 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구독 복원 실패', tag: _tag, error: e);
    }
  }

  /// 🧪 테스트 환경에서 구독 강제 활성화 (디버깅용)
  Future<bool> forceActivateSubscriptionForTesting() async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('사용자 ID가 없습니다', tag: _tag);
        return false;
      }

      LoggerUtils.logInfo('🧪 테스트 환경: 구독 강제 활성화 시작', tag: _tag);

      final now = DateTime.now();
      final endDate = DateTime(now.year, now.month + 1, now.day); // 1개월 후

      // 플랫폼 정보 수집
      final platformInfo = await _getCurrentPlatformInfo();

      // 테스트용 구독 정보 생성
      final subscription = UserSubscription.createPlus(
        userId: _currentUserId!,
        startDate: now,
        endDate: endDate,
        originalPurchaserUserId: _currentUserId!,
        platformAccountId: platformInfo['platformAccountId'],
        platform: platformInfo['platform'],
        purchaseId: 'test_force_${_currentUserId}_${now.millisecondsSinceEpoch}',
        productId: _plusPlanProductId,
      );

      // Firebase에 저장
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .set(subscription.toFirebaseMap());

      // SubscriptionService 업데이트
      final subscriptionService = SubscriptionService();
      await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.plus);

      // Provider 갱신
      if (_ref != null) {
        _ref!.invalidate(subscriptionNotifierProvider);
        _ref!.invalidate(currentPlanTypeProvider);
        _ref!.invalidate(isPlusUserProvider);
        _ref!.invalidate(currentSubscriptionProvider);
        _ref!.invalidate(hasServiceFeatureProvider);
        _ref!.invalidate(hasExcelExportFeatureProvider);
      }

      LoggerUtils.logInfo('🧪 테스트 환경: 구독 강제 활성화 완료', tag: _tag);
      return true;
    } catch (e) {
      LoggerUtils.logError('테스트 구독 활성화 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 현재 활성 구독 조회
  Future<List<PurchaseDetails>> getActiveSubscriptions() async {
    try {
      print('🔍 [구독조회] 활성 구독 조회 시작');

      // 과거 구매 내역 조회
      print('🔄 [구독조회] 구매 내역 복원 시작...');
      await _inAppPurchase.restorePurchases();
      print('✅ [구독조회] 구매 내역 복원 완료');

      // 잠시 대기하여 복원 이벤트가 처리될 시간을 줌
      await Future.delayed(const Duration(milliseconds: 500));

      print('🔍 [구독조회] 플랫폼: ${Platform.isIOS ? "iOS" : "Android"}');

      // Firebase에서 현재 사용자의 구독 상태 확인
      try {
        if (_currentUserId != null) {
          print('🔍 [구독조회] Firebase에서 구독 상태 확인 중...');
          final subscriptionDoc = await _firestore
              .collection('users')
              .doc(_currentUserId!)
              .collection('subscriptions')
              .doc('current')
              .get();

          if (subscriptionDoc.exists && subscriptionDoc.data() != null) {
            final data = subscriptionDoc.data()!;
            final planType = data['planType'] as String?;
            final isActive = data['isActive'] as bool? ?? false;
            final endDate = data['subscriptionEndDate'] as String?;
            final purchaseId = data['purchaseId'] as String?;
            final productId = data['productId'] as String?;

            print('📊 [구독조회] Firebase 구독 정보: planType=$planType, isActive=$isActive, endDate=$endDate');

            if (planType == 'SubscriptionPlanType.plus' && isActive && endDate != null) {
              final endDateTime = DateTime.parse(endDate);
              if (endDateTime.isAfter(DateTime.now())) {
                print('✅ [구독조회] Firebase에서 활성 플러스 구독 확인됨');

                // 🔧 수정: 실제 PurchaseDetails 객체 생성하여 반환
                // iOS에서는 가상의 PurchaseDetails를 생성하여 기존 구독으로 인식시킴
                try {
                  final mockPurchaseDetails = _createMockPurchaseDetails(
                    productId: productId ?? _plusPlanProductId,
                    purchaseId: purchaseId ?? 'firebase_subscription_${DateTime.now().millisecondsSinceEpoch}',
                  );
                  print('🎯 [구독조회] 가상 PurchaseDetails 생성 완료');
                  return [mockPurchaseDetails];
                } catch (e) {
                  print('⚠️ [구독조회] 가상 PurchaseDetails 생성 실패: $e');
                  // 생성 실패 시에도 빈 배열이 아닌 기본 처리
                }
              } else {
                print('⚠️ [구독조회] 구독이 만료됨: $endDateTime');
              }
            } else {
              print('📊 [구독조회] 활성 플러스 구독 없음 (planType=$planType, isActive=$isActive)');
            }
          } else {
            print('📊 [구독조회] Firebase 구독 문서 없음');
          }
        } else {
          print('⚠️ [구독조회] 사용자 ID 없음');
        }
      } catch (e) {
        print('❌ [구독조회] Firebase 구독 상태 확인 실패: $e');
      }

      print('❌ [구독조회] 활성 구독 없음');
      return [];
    } catch (e) {
      print('❌ [구독조회] 활성 구독 조회 실패: $e');
      return [];
    }
  }

  /// 가상의 PurchaseDetails 생성 (Firebase 기반 구독 복원용)
  PurchaseDetails _createMockPurchaseDetails({
    required String productId,
    required String purchaseId,
  }) {
    // in_app_purchase 패키지의 내부 구현을 사용하여 가상 객체 생성
    // 이는 기존 구독 복원 처리를 위한 임시 방편입니다.

    // 플랫폼별 처리
    if (Platform.isIOS) {
      // iOS용 가상 PurchaseDetails
      return PurchaseDetails(
        productID: productId,
        purchaseID: purchaseId,
        transactionDate: DateTime.now().toIso8601String(),
        verificationData: PurchaseVerificationData(
          localVerificationData: 'mock_verification_data',
          serverVerificationData: 'mock_server_data',
          source: 'app_store',
        ),
        status: PurchaseStatus.purchased,
      );
    } else {
      // Android용 가상 PurchaseDetails
      return PurchaseDetails(
        productID: productId,
        purchaseID: purchaseId,
        transactionDate: DateTime.now().toIso8601String(),
        verificationData: PurchaseVerificationData(
          localVerificationData: 'mock_verification_data',
          serverVerificationData: 'mock_server_data',
          source: 'google_play',
        ),
        status: PurchaseStatus.purchased,
      );
    }
  }

  /// 정확한 다음 달 날짜 계산 (월말 날짜 처리 개선)
  DateTime _calculateNextMonthDate(DateTime date) {
    // 다음 달의 년도와 월 계산
    int nextYear = date.year;
    int nextMonth = date.month + 1;

    // 12월인 경우 다음 해 1월로 처리
    if (nextMonth > 12) {
      nextYear++;
      nextMonth = 1;
    }

    // 다음 달의 마지막 날 계산
    final lastDayOfNextMonth = DateTime(nextYear, nextMonth + 1, 0).day;

    // 현재 날짜가 다음 달의 마지막 날보다 크면 마지막 날로 조정
    final targetDay = date.day > lastDayOfNextMonth ? lastDayOfNextMonth : date.day;

    return DateTime(nextYear, nextMonth, targetDay);
  }

  /// 구매 상태 변경 처리
  void _onPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    print('🔥 [구매] 상태 업데이트 수신 - 항목 수: ${purchaseDetailsList.length}');

    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      print('🔥 [구매] 상태 변경: ${purchaseDetails.status}, productID: ${purchaseDetails.productID}');
      print('📱 [구매] 세부정보: purchaseID=${purchaseDetails.purchaseID}, transactionDate=${purchaseDetails.transactionDate}');
      print('🔐 [구매] pendingCompletePurchase: ${purchaseDetails.pendingCompletePurchase}');

      try {
        switch (purchaseDetails.status) {
          case PurchaseStatus.pending:
            print('⏳ [구매] 대기 중 - 결제 승인 대기');
            break;
          case PurchaseStatus.purchased:
            print('🎉 [구매] 성공! 처리 시작...');
            print('🔍 [구매] 성공 상세: productID=${purchaseDetails.productID}, purchaseID=${purchaseDetails.purchaseID}');
            await _handleSuccessfulPurchase(purchaseDetails);
            print('✅ [구매] 성공 처리 완료');
            break;
          case PurchaseStatus.error:
            print('❌ [구매] 실패: ${purchaseDetails.error}');
            print('❌ [구매] 실패 상세: ${purchaseDetails.error?.message}');
            break;
          case PurchaseStatus.restored:
            print('🔄 [구매] 구독 복원! 처리 시작...');
            print('🔍 [구매] 복원 상세: productID=${purchaseDetails.productID}, purchaseID=${purchaseDetails.purchaseID}');
            await _handleRestoredPurchase(purchaseDetails);
            print('✅ [구매] 구독 복원 처리 완료');
            break;
          case PurchaseStatus.canceled:
            print('❌ [구매] 취소됨');
            break;
        }
      } catch (e) {
        print('❌ [구매] 처리 중 오류 발생: $e');
        print('❌ [구매] 오류 발생 위치: productID=${purchaseDetails.productID}, status=${purchaseDetails.status}');
      } finally {
        // 🔥 중요: 예외가 발생해도 반드시 completePurchase 호출
        if (purchaseDetails.pendingCompletePurchase) {
          try {
            print('🔄 [구매] 완료 처리 시작: ${purchaseDetails.productID}');
            await _inAppPurchase.completePurchase(purchaseDetails);
            print('✅ [구매] 완료 처리됨: ${purchaseDetails.productID}');
          } catch (e) {
            print('❌ [구매] 완료 처리 실패: $e');
          }
        } else {
          print('ℹ️ [구매] 완료 처리 불필요: ${purchaseDetails.productID}');
        }
      }
    }

    print('🏁 [구매] 모든 상태 업데이트 처리 완료');
  }

  /// 성공한 구매 처리
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      print('🎉 [구매처리] 성공 처리 시작: ${purchaseDetails.productID}');

      // 🔐 사용자 ID 확인 (구매 시작 시점 저장된 ID 우선 사용)
      final String? userId = _purchaseStartUserId ?? _currentUserId;
      if (userId == null) {
        print('❌ [구매처리] 사용자 ID를 찾을 수 없음 - 구매 처리 불가');
        print('[구매처리] 구매 시작 ID: $_purchaseStartUserId, 현재 ID: $_currentUserId');
        return;
      }
      print('✅ [구매처리] 사용자 ID 확인됨: $userId');

      if (purchaseDetails.productID == _plusPlanProductId) {
        print('✅ [구매처리] 플러스 플랜 구독 확인됨 - 활성화 시작');
        // 플러스 플랜 구독 활성화
        await _activatePlusSubscription(purchaseDetails, userId);
      } else {
        print('⚠️ [구매처리] 알 수 없는 상품 ID: ${purchaseDetails.productID}');
      }

      print('✅ [구매처리] 성공 처리 완료');
    } catch (e) {
      print('❌ [구매처리] 성공 처리 실패: $e');
      // 예외가 발생해도 rethrow하지 않음 - completePurchase가 호출되도록 함
    }
  }

  /// 복원된 구매 처리 (🔐 보안 강화)
  Future<void> _handleRestoredPurchase(PurchaseDetails purchaseDetails) async {
    try {
      print('🔐 [구독복원] 보안 강화된 구매 복원 처리 시작: ${purchaseDetails.productID}');

      // 🔐 사용자 인증 상태 강제 확인
      if (_auth.currentUser == null) {
        print('❌ [구독복원] 사용자가 로그인되어 있지 않음 - 복원 중단');
        return;
      }

      print('✅ [구독복원] 사용자 인증 상태 확인됨: ${_auth.currentUser!.uid}');

      // 🔐 Firebase 토큰 강제 갱신 (권한 오류 방지)
      try {
        print('🔐 [구독복원] Firebase 토큰 갱신 시작...');

        // App Check 토큰 갱신
        try {
          await FirebaseAppCheck.instance.getToken(true);
          print('✅ [구독복원] App Check 토큰 갱신 완료');
        } catch (e) {
          print('⚠️ [구독복원] App Check 토큰 갱신 실패 (무시): $e');
        }

        // Auth ID 토큰 갱신
        try {
          await _auth.currentUser!.getIdToken(true);
          print('✅ [구독복원] Auth ID 토큰 갱신 완료');
        } catch (e) {
          print('⚠️ [구독복원] Auth ID 토큰 갱신 실패 (무시): $e');
        }

        // 토큰 갱신 후 잠시 대기
        await Future.delayed(const Duration(milliseconds: 500));
        print('✅ [구독복원] Firebase 토큰 갱신 완료');
      } catch (e) {
        print('⚠️ [구독복원] Firebase 토큰 갱신 중 오류: $e');
      }

      if (purchaseDetails.productID == _plusPlanProductId) {
        // 🔐 구독 복원 권한 확인
        print('🔍 [구독복원] 구독 복원 권한 확인 중...');
        final canRestore = await _canRestoreSubscription(purchaseDetails);

        if (canRestore) {
          print('✅ [구독복원] 구독 복원 권한 확인됨 - 복원 진행');
          await _activatePlusSubscription(purchaseDetails);
        } else {
          print('🚨 [구독복원] 구독 복원 권한 없음 - 다른 계정의 구독입니다');
          // 복원 실패를 사용자에게 알림 (필요시 UI에서 처리)
        }
      }

      print('✅ [구독복원] 구매 복원 처리 완료');
    } catch (e) {
      print('❌ [구독복원] 구매 복원 처리 실패: $e');
    }
  }

  /// 플러스 플랜 구독 활성화 (🔐 보안 강화)
  Future<void> _activatePlusSubscription(PurchaseDetails purchaseDetails, [String? userId]) async {
    try {
      // 🔐 사용자 ID 확인 (매개변수로 전달된 ID 우선 사용)
      final String? finalUserId = userId ?? _purchaseStartUserId ?? _currentUserId;
      if (finalUserId == null) {
        LoggerUtils.logError('사용자 ID가 없습니다', tag: _tag);
        return;
      }

      print('🔐 [구독활성화] 보안 강화된 플러스 플랜 구독 활성화 시작');
      print('[구독활성화] 구매 상세 정보: productID=${purchaseDetails.productID}, purchaseID=${purchaseDetails.purchaseID}');

      final now = DateTime.now();
      // 🔧 수정: 더 정확한 월 계산 (월말 날짜 처리 개선)
      final endDate = _calculateNextMonthDate(now);

      // 🔐 현재 사용자의 플랫폼 계정 정보 수집
      final platformInfo = await _getCurrentPlatformInfo();

      print('🔐 [구독활성화] 플랫폼 정보: ${platformInfo['platform']}, 계정: ${platformInfo['platformAccountId']}');

      // 🔐 테스트 환경에서 purchaseID가 null일 수 있으므로 대체값 생성
      String? finalPurchaseId = purchaseDetails.purchaseID;
      if (finalPurchaseId == null || finalPurchaseId.isEmpty) {
        // 테스트 환경에서는 임시 ID 생성
        finalPurchaseId = 'test_${_currentUserId}_${now.millisecondsSinceEpoch}';
        print('⚠️ [구독활성화] 테스트 환경: purchaseID가 null이므로 임시 ID 생성: $finalPurchaseId');
      }

      // 🔐 보안 강화된 구독 정보로 UserSubscription 생성
      final subscription = UserSubscription.createPlus(
        userId: finalUserId,
        startDate: now,
        endDate: endDate,
        originalPurchaserUserId: finalUserId, // 원본 구매자 = 현재 사용자
        platformAccountId: platformInfo['platformAccountId'],
        platform: platformInfo['platform'],
        purchaseId: finalPurchaseId,
        productId: purchaseDetails.productID,
      );

      final subscriptionMap = subscription.toFirebaseMap();
      print('[구독활성화] 생성된 구독 정보: $subscriptionMap');

      // Firebase에 보안 강화된 구독 정보 저장 (App Check/토큰 갱신 포함 재시도)
      print('🔥 [구독활성화] Firebase 저장 시작 (재시도 포함)...');
      await _saveSubscriptionWithRetries(finalUserId, subscriptionMap);
      print('✅ [구독활성화] Firebase에 구독 정보 저장 완료');

      // 🔄 SubscriptionService 동기화 (보안 정보 보존됨)
      print('🔄 [구독활성화] SubscriptionService 동기화 시작 (보안 정보 보존)...');
      try {
        final subscriptionService = SubscriptionService();
        await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.plus);
        print('✅ [구독활성화] SubscriptionService 동기화 완료');
      } catch (e) {
        print('⚠️ [구독활성화] SubscriptionService 동기화 실패 - Firebase 저장은 성공했으므로 계속 진행: $e');
        // Firebase 저장은 성공했으므로 이 오류로 인해 전체 프로세스를 실패시키지 않음
      }

      // 모든 구독 관련 Provider 즉시 갱신
      print('🔄 [구독활성화] Provider 갱신 시작...');
      if (_ref != null) {
        _ref!.invalidate(subscriptionNotifierProvider);
        _ref!.invalidate(currentPlanTypeProvider);
        _ref!.invalidate(isPlusUserProvider);
        _ref!.invalidate(currentSubscriptionProvider);
        _ref!.invalidate(featureAccessProvider('sellerManagement'));
        _ref!.invalidate(featureAccessProvider('serverSync'));
        _ref!.invalidate(featureAccessProvider('setDiscount'));
        _ref!.invalidate(featureAccessProvider('service'));
        _ref!.invalidate(featureAccessProvider('excelExport'));
        _ref!.invalidate(featureAccessProvider('pdfExport'));
        _ref!.invalidate(featureAccessProvider('advancedStats'));
        _ref!.invalidate(hasSellerManagementFeatureProvider);
        _ref!.invalidate(hasServerSyncFeatureProvider);
        _ref!.invalidate(hasSetDiscountFeatureProvider);
        _ref!.invalidate(hasServiceFeatureProvider);
        _ref!.invalidate(hasExcelExportFeatureProvider);
        print('✅ [구독활성화] 모든 구독 관련 Provider 갱신 완료');
      } else {
        print('⚠️ [구독활성화] _ref가 null이어서 Provider 갱신을 건너뜀');
      }

      print('🎉 [구독활성화] 플러스 플랜 구독 활성화 완료!');

      // 🔐 구매 완료 후 저장된 사용자 ID 초기화
      _purchaseStartUserId = null;
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 활성화 실패', tag: _tag, error: e);
      // 실패 시에도 저장된 사용자 ID 초기화
      _purchaseStartUserId = null;
    }
  }

  /// 🔐 현재 사용자의 플랫폼 계정 정보 수집
  /// Firebase 저장 재시도 유틸 (App Check/ID 토큰 강제 갱신 포함)
  Future<void> _saveSubscriptionWithRetries(String userId, Map<String, dynamic> data) async {
    const int maxAttempts = 3;
    int attempt = 0;

    print('🔥 [Firebase] 구독 정보 저장 시작 (최대 $maxAttempts회 시도)');
    print('📊 [Firebase] 저장할 데이터: $data');

    while (true) {
      attempt++;
      print('🔄 [Firebase] 저장 시도 $attempt/$maxAttempts');

      try {
        // 🔐 사용자 인증 상태 재확인 (권한 오류 방지)
        if (_auth.currentUser == null) {
          print('❌ [Firebase] 사용자가 로그아웃됨 - 저장 중단');
          throw Exception('사용자가 로그아웃되어 구독 정보를 저장할 수 없습니다');
        }

        // 현재 사용자 ID와 저장하려는 userId가 일치하는지 확인
        if (_auth.currentUser!.uid != userId) {
          print('❌ [Firebase] 사용자 ID 불일치 - 현재: ${_auth.currentUser!.uid}, 저장 대상: $userId');
          throw Exception('사용자 ID가 일치하지 않아 구독 정보를 저장할 수 없습니다');
        }

        // App Check 토큰 강제 갱신 시도 (무시 가능)
        print('🔐 [Firebase] App Check 토큰 갱신 시도...');
        try {
          await FirebaseAppCheck.instance.getToken(true);
          print('✅ [Firebase] App Check 토큰 갱신 완료');
        } catch (e) {
          print('⚠️ [Firebase] App Check 토큰 갱신 실패 (무시): $e');
        }

        // Firebase Auth ID 토큰 강제 갱신 (권한 오류 대비)
        print('🔐 [Firebase] Auth ID 토큰 갱신 시도...');
        try {
          await _auth.currentUser!.getIdToken(true);
          print('✅ [Firebase] Auth ID 토큰 갱신 완료');
        } catch (e) {
          print('⚠️ [Firebase] Auth ID 토큰 갱신 실패 (무시): $e');
        }

        // 토큰 갱신 후 잠시 대기
        await Future.delayed(const Duration(milliseconds: 300));

        print('📝 [Firebase] Firestore에 구독 정보 저장 중...');
        print('📊 [Firebase] 저장 경로: /users/$userId/subscriptions/current');
        print('📊 [Firebase] 저장 데이터: ${data.keys.toList()}');

        await _firestore
            .collection('users')
            .doc(userId)
            .collection('subscriptions')
            .doc('current')
            .set(data);

        print('✅ [Firebase] 저장 성공! (시도 $attempt/$maxAttempts)');
        return; // 성공
      } catch (e) {
        print('❌ [Firebase] 저장 실패 (시도 $attempt/$maxAttempts): $e');
        print('📊 [Firebase] 오류 타입: ${e.runtimeType}');
        print('📊 [Firebase] 오류 상세: $e');

        // 권한 오류인 경우 추가 정보 출력
        if (e.toString().contains('permission') || e.toString().contains('insufficient')) {
          print('🚨 [Firebase] 권한 오류 감지 - 사용자 인증 상태 재확인');
          print('📊 [Firebase] 현재 사용자: ${_auth.currentUser?.uid}');
          print('📊 [Firebase] 저장 대상 사용자: $userId');
          print('📊 [Firebase] 사용자 로그인 상태: ${_auth.currentUser != null}');
        }

        if (attempt >= maxAttempts) {
          print('❌ [Firebase] 저장 최종 실패 - 모든 재시도 소진: $e');
          rethrow;
        }

        final backoffMs = 400 * attempt;
        print('⏳ [Firebase] 저장 재시도 대기 중... (${backoffMs}ms)');
        await Future.delayed(Duration(milliseconds: backoffMs));
      }
    }
  }

  /// 🔐 현재 사용자의 플랫폼 계정 정보 수집
  Future<Map<String, String?>> _getCurrentPlatformInfo() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('Firebase 사용자가 없습니다', tag: _tag);
        return {'platform': null, 'platformAccountId': null};
      }

      // 사용자의 provider 정보 확인
      for (final providerData in user.providerData) {
        switch (providerData.providerId) {
          case 'apple.com':
            LoggerUtils.logInfo('🍎 Apple 로그인 사용자 감지', tag: _tag);
            return {
              'platform': 'apple',
              'platformAccountId': providerData.uid, // Apple ID
            };
          case 'google.com':
            LoggerUtils.logInfo('🔍 Google 로그인 사용자 감지', tag: _tag);
            return {
              'platform': 'google',
              'platformAccountId': providerData.uid, // Google Account ID
            };
        }
      }

      // 이메일/패스워드 로그인이거나 알 수 없는 경우
      LoggerUtils.logWarning('알 수 없는 로그인 방법 또는 이메일 로그인', tag: _tag);
      return {
        'platform': Platform.isIOS ? 'apple' : 'google', // 플랫폼 기본값
        'platformAccountId': user.uid, // Firebase UID 사용
      };
    } catch (e) {
      LoggerUtils.logError('플랫폼 정보 수집 실패', tag: _tag, error: e);
      return {
        'platform': Platform.isIOS ? 'apple' : 'google',
        'platformAccountId': null,
      };
    }
  }

  /// 🔐 구독 복원 권한 확인 (Firebase 권한 오류 해결)
  Future<bool> _canRestoreSubscription(PurchaseDetails purchaseDetails) async {
    try {
      if (_currentUserId == null) {
        print('❌ [권한확인] 사용자 ID가 없습니다');
        return false;
      }

      print('🔐 [권한확인] 구독 복원 권한 확인 시작');
      print('📊 [권한확인] 복원할 구매 ID: ${purchaseDetails.purchaseID}');
      print('📊 [권한확인] 복원할 상품 ID: ${purchaseDetails.productID}');

      // 🔧 수정: collectionGroup 쿼리 제거 (Firebase 권한 오류 해결)
      // 현재 사용자의 구독 상태만 확인 (보안 규칙 준수)
      try {
        print('🔍 [권한확인] 현재 사용자 구독 상태 확인 중...');
        final subscriptionDoc = await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('subscriptions')
            .doc('current')
            .get();

        if (!subscriptionDoc.exists || subscriptionDoc.data() == null) {
          print('✅ [권한확인] 기존 구독 없음 - 새로운 구독으로 처리');
          return true; // 새로운 구독이므로 허용
        }

        final existingSubscription = subscriptionDoc.data()!;
        final existingPurchaseId = existingSubscription['purchaseId'] as String?;
        final existingProductId = existingSubscription['productId'] as String?;
        final planType = existingSubscription['planType'] as String?;
        final isActive = existingSubscription['isActive'] as bool? ?? false;

        print('📊 [권한확인] 기존 구독 정보:');
        print('  - 기존 구매 ID: $existingPurchaseId');
        print('  - 기존 상품 ID: $existingProductId');
        print('  - 플랜 타입: $planType');
        print('  - 활성 상태: $isActive');

        // 동일한 구매 ID인 경우 (이미 처리된 구독)
        if (existingPurchaseId == purchaseDetails.purchaseID &&
            existingProductId == purchaseDetails.productID) {
          print('🔄 [권한확인] 동일한 구매 ID - 기존 구독 업데이트');
          return true; // 동일 구매이므로 허용
        }

        // 다른 활성 구독이 있는 경우
        if (planType == 'SubscriptionPlanType.plus' && isActive) {
          print('⚠️ [권한확인] 다른 활성 구독 존재 - 기존 구독 교체');
          return true; // 새로운 구독으로 교체 허용
        }

        print('✅ [권한확인] 구독 복원 허용');
        return true;

      } catch (e) {
        print('❌ [권한확인] Firebase 구독 확인 실패: $e');
        // Firebase 접근 실패 시에도 복원 허용 (새로운 구독으로 처리)
        return true;
      }

    } catch (e) {
      print('❌ [권한확인] 구독 복원 권한 확인 실패: $e');
      return false; // 전체 오류 시 복원 거부
    }
  }
  /// iOS 오퍼 코드(프로모션/Offer Code) 리딤 시트 표시
  Future<void> presentOfferCodeRedemptionSheet() async {
    if (!Platform.isIOS) {
      LoggerUtils.logInfo('오퍼 코드 리딤 시트는 iOS에서만 지원됩니다', tag: _tag);
      return;
    }
    try {
      LoggerUtils.logInfo('iOS 오퍼 코드 리딤 시트 표시 요청', tag: _tag);
      final addition = _inAppPurchase
          .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      await addition.presentCodeRedemptionSheet();

      // 시트 종료 후 즉시 권한 반영을 위해 복원 1회 호출
      await _inAppPurchase.restorePurchases();
      LoggerUtils.logInfo('오퍼 코드 리딤 시트 처리 완료(복원 요청 포함)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('오퍼 코드 리딤 시트 표시 실패', tag: _tag, error: e);
    }
  }

}
